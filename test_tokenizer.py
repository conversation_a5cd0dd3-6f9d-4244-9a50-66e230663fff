#!/usr/bin/env python3

# 测试tokenizer的行为
from transformers import AutoTokenizer, AutoProcessor

# 使用Qwen2-VL的tokenizer
try:
    processor = AutoProcessor.from_pretrained("/data/wuyang/PLM/Qwen2-VL-2B-Instruct")
    tokenizer = processor.tokenizer
except:
    # 如果上面的路径不存在，使用一个通用的tokenizer
    tokenizer = AutoTokenizer.from_pretrained("Qwen/Qwen2-VL-2B-Instruct")

# 测试不同的文本
test_texts = [
    "angry",
    "</answer>",
    "<|im_end|>",
    "</answer><|im_end|>",
    "<answer>angry</answer><|im_end|>"
]

print("=== Tokenizer测试 ===")
for text in test_texts:
    tokens = tokenizer.encode(text, add_special_tokens=False)
    decoded = tokenizer.decode(tokens)
    print(f"文本: '{text}'")
    print(f"Tokens: {tokens}")
    print(f"Token数量: {len(tokens)}")
    print(f"解码回来: '{decoded}'")
    print("-" * 50)

# 特别测试angry
print("\n=== 详细分析 'angry' ===")
angry_tokens = tokenizer.encode("angry", add_special_tokens=False)
print(f"'angry' 的tokens: {angry_tokens}")
print(f"Token数量: {len(angry_tokens)}")

# 逐个解码每个token
for i, token in enumerate(angry_tokens):
    decoded_token = tokenizer.decode([token])
    print(f"Token {i}: {token} -> '{decoded_token}'")

# 测试特殊token
print("\n=== 特殊Token测试 ===")
special_tokens = ["<|im_end|>", "</answer>"]
for token_text in special_tokens:
    tokens = tokenizer.encode(token_text, add_special_tokens=False)
    print(f"'{token_text}' -> {tokens} (数量: {len(tokens)})")
