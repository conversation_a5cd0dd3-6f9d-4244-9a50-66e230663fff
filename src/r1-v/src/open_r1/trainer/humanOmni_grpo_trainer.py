# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
import textwrap
from collections import defaultdict
from typing import Any, Callable, Optional, Union
from qwen_vl_utils import process_vision_info
import torch
import torch.utils.data
import transformers
from datasets import Dataset, IterableDataset
from packaging import version
from transformers import (
    AriaForConditionalGeneration,
    AriaProcessor,
    AutoModelForCausalLM,
    AutoModelForSequenceClassification,
    AutoProcessor,
    AutoTokenizer,
    GenerationConfig,
    PreTrainedModel,
    PreTrainedTokenizerBase,
    Qwen2VLForConditionalGeneration,
    Qwen2_5_VLForConditionalGeneration,
    Trainer,
    Trainer<PERSON><PERSON>back,
    is_wandb_available,
)
from transformers.integrations.deepspeed import is_deepspeed_zero3_enabled
from transformers.utils import is_peft_available

from trl.data_utils import apply_chat_template, is_conversational, maybe_apply_chat_template
from trl.models import create_reference_model, prepare_deepspeed, unwrap_model_for_generation
from trl.trainer.grpo_config import GRPOConfig
from trl.trainer.utils import generate_model_card, get_comet_experiment_url

import copy
from transformers import BertModel, BertTokenizer
from humanomni.model import *
from humanomni.constants import NUM_FRAMES, IGNORE_INDEX, MODAL_INDEX_MAP, DEFAULT_X_START_TOKEN, DEFAULT_X_END_TOKEN
from humanomni.mm_utils import tokenizer_multimodal_token, process_image, process_video, read_video_patch, process_audio, frame_sample,get_model_name_from_path
from humanomni import model_init, mm_infer
from humanomni.constants import DEFAULT_IMAGE_TOKEN, DEFAULT_VIDEO_TOKEN
from transformers import (
    CLIPVisionModel, CLIPImageProcessor, CLIPVisionConfig,
    SiglipVisionModel, SiglipImageProcessor, SiglipVisionConfig,
     WhisperFeatureExtractor, WhisperProcessor, WhisperConfig, WhisperForAudioClassification
)
import os
import sys
from src.open_r1.trainer.grpo_trainer import Qwen2VLGRPOTrainer
sys.path.append('/mnt/data/jiaxing.zjx/code/HumanOmni/')
sys.path.append('/mnt/data/jiaxing.zjx/cache/huggingface/')
#初始化BERT分词器
# bert_model = "bert-base-uncased"
# bert_tokenizer = BertTokenizer.from_pretrained(bert_model)

def check_parameters(model):
    frozen_params = []
    trainable_params = []

    for name, param in model.named_parameters():
        if param.requires_grad:
            trainable_params.append(name)
        else:
            frozen_params.append(name)

    print("Frozen Parameters:")
    for name in frozen_params:
        print(name)
    
    print("\nTrainable Parameters:")
    for name in trainable_params:
        print(name)



if is_peft_available():
    from peft import PeftConfig, get_peft_model

if is_wandb_available():
    import wandb

# What we call a reward function is a callable that takes a list of prompts and completions and returns a list of
# rewards. When it's a string, it's a model ID, so it's loaded as a pretrained model.
RewardFunc = Union[str, PreTrainedModel, Callable[[list, list], list[float]]]

from moviepy.editor import VideoFileClip

def get_video_duration(video_path):
    """根据视频路径获取视频的时长"""
    try:
        clip = VideoFileClip(video_path)
        duration = clip.duration
        clip.close()
        return duration
    except Exception as e:
        print(f"Error reading video file {video_path}: {e}")
        return None

class HumanOmniVLGRPOTrainer(Trainer):
    """
    Trainer for the Group Relative Policy Optimization (GRPO) method. This algorithm was initially proposed in the
    paper [DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models](https://huggingface.co/papers/2402.03300).

    Example:

    ```python
    from datasets import load_dataset
    from trl import GRPOTrainer

    dataset = load_dataset("trl-lib/tldr", split="train")

    trainer = GRPOTrainer(
        model="Qwen/Qwen2-0.5B-Instruct",
        reward_funcs="weqweasdas/RM-Gemma-2B",
        train_dataset=dataset,
    )

    trainer.train()
    ```

    Args:
        model (`Union[str, PreTrainedModel]`):
            Model to be trained. Can be either:

            - A string, being the *model id* of a pretrained model hosted inside a model repo on huggingface.co, or
              a path to a *directory* containing model weights saved using
              [`~transformers.PreTrainedModel.save_pretrained`], e.g., `'./my_model_directory/'`. The model is
              loaded using [`~transformers.AutoModelForCausalLM.from_pretrained`] with the keywork arguments
              in `args.model_init_kwargs`.
            - A [`~transformers.PreTrainedModel`] object. Only causal language models are supported.
        reward_funcs (`Union[RewardFunc, list[RewardFunc]]`):
            Reward functions to be used for computing the rewards. To compute the rewards, we call all the reward
            functions with the prompts and completions and sum the rewards. Can be either:

            - A single reward function, such as:
                - A string: The *model ID* of a pretrained model hosted inside a model repo on huggingface.co, or a
                path to a *directory* containing model weights saved using
                [`~transformers.PreTrainedModel.save_pretrained`], e.g., `'./my_model_directory/'`. The model is loaded
                using [`~transformers.AutoModelForSequenceClassification.from_pretrained`] with `num_labels=1` and the
                keyword arguments in `args.model_init_kwargs`.
                - A [`~transformers.PreTrainedModel`] object: Only sequence classification models are supported.
                - A custom reward function: The function is provided with the prompts and the generated completions,
                  plus any additional columns in the dataset. It should return a list of rewards. For more details, see
                  [Using a custom reward function](#using-a-custom-reward-function).
            - A list of reward functions, where each item can independently be any of the above types. Mixing different
            types within the list (e.g., a string model ID and a custom reward function) is allowed.
        args ([`GRPOConfig`], *optional*, defaults to `None`):
            Configuration for this trainer. If `None`, a default configuration is used.
        train_dataset ([`~datasets.Dataset`] or [`~datasets.IterableDataset`]):
            Dataset to use for training. It must include a column `"prompt"`. Any additional columns in the dataset is
            ignored. The format of the samples can be either:

            - [Standard](dataset_formats#standard): Each sample contains plain text.
            - [Conversational](dataset_formats#conversational): Each sample contains structured messages (e.g., role
              and content).
        eval_dataset ([`~datasets.Dataset`], [`~datasets.IterableDataset`] or `dict[str, Union[Dataset, IterableDataset]]`):
            Dataset to use for evaluation. It must meet the same requirements as `train_dataset`.
        processing_class ([`~transformers.PreTrainedTokenizerBase`], *optional*, defaults to `None`):
            Processing class used to process the data. The padding side must be set to "left". If `None`, the
            processing class is loaded from the model's name with [`~transformers.AutoTokenizer.from_pretrained`].
        reward_processing_classes (`Union[PreTrainedTokenizerBase, list[PreTrainedTokenizerBase]]`, *optional*, defaults to `None`):
            Processing classes corresponding to the reward functions specified in `reward_funcs`. Can be either:

            - A single processing class: Used when `reward_funcs` contains only one reward function.
            - A list of processing classes: Must match the order and length of the reward functions in `reward_funcs`.
            If set to `None`, or if an element of the list corresponding to a [`~transformers.PreTrainedModel`] is
            `None`, the tokenizer for the model is automatically loaded using [`~transformers.AutoTokenizer.from_pretrained`].
            For elements in `reward_funcs` that are custom reward functions (not [`~transformers.PreTrainedModel`]),
            the corresponding entries in `reward_processing_classes` are ignored.
        callbacks (list of [`~transformers.TrainerCallback`], *optional*, defaults to `None`):
            List of callbacks to customize the training loop. Will add those to the list of default callbacks
            detailed in [here](https://huggingface.co/docs/transformers/main_classes/callback).

            If you want to remove one of the default callbacks used, use the [`~transformers.Trainer.remove_callback`]
            method.
        optimizers (`tuple[torch.optim.Optimizer, torch.optim.lr_scheduler.LambdaLR]`, *optional*, defaults to `(None, None)`):
            A tuple containing the optimizer and the scheduler to use. Will default to an instance of [`AdamW`] on your
            model and a scheduler given by [`get_linear_schedule_with_warmup`] controlled by `args`.
        peft_config ([`~peft.PeftConfig`], *optional*, defaults to `None`):
            PEFT configuration used to wrap the model. If `None`, the model is not wrapped.
    """

    def __init__(
        self,
        model: Union[str, PreTrainedModel],
        reward_funcs: Union[RewardFunc, list[RewardFunc]],
        args: GRPOConfig = None,
        train_dataset: Optional[Union[Dataset, IterableDataset]] = None,
        eval_dataset: Optional[Union[Dataset, IterableDataset, dict[str, Union[Dataset, IterableDataset]]]] = None,
        processing_class: Optional[PreTrainedTokenizerBase] = None,
        reward_processing_classes: Optional[Union[PreTrainedTokenizerBase, list[PreTrainedTokenizerBase]]] = None,
        callbacks: Optional[list[TrainerCallback]] = None,
        optimizers: tuple[Optional[torch.optim.Optimizer], Optional[torch.optim.lr_scheduler.LambdaLR]] = (None, None),
        peft_config: Optional["PeftConfig"] = None,
        max_pixels: Optional[int] = 12845056,
        min_pixels: Optional[int] = 3136,
        attn_implementation: str = "flash_attention_2",
    ):
        # Args
       # import ipdb;ipdb.set_trace()
        if args is None:
            model_name = model if isinstance(model, str) else model.config._name_or_path
            model_name = model_name.split("/")[-1]
            args = GRPOConfig(f"{model_name}-GRPO")

        # Models
        # Trained model
        model_init_kwargs = args.model_init_kwargs or {}
        model_init_kwargs["attn_implementation"] = attn_implementation
        if isinstance(model, str):
            model_id = model
            model_name_d = model_id
            torch_dtype = model_init_kwargs.get("torch_dtype")
            if isinstance(torch_dtype, torch.dtype) or torch_dtype == "auto" or torch_dtype is None:
                pass  # torch_dtype is already a torch.dtype or "auto" or None
            elif isinstance(torch_dtype, str):  # it's a str, but not "auto"
                torch_dtype = getattr(torch, torch_dtype)
                model_init_kwargs["torch_dtype"] = torch_dtype
            else:
                raise ValueError(
                    "Invalid `torch_dtype` passed to `GRPOConfig`. Expected either 'auto' or a string representing "
                    f"a `torch.dtype` (e.g., 'float32'), but got {torch_dtype}."
                )
            # Disable caching if gradient checkpointing is enabled (not supported)
            model_init_kwargs["use_cache"] = (
                False if args.gradient_checkpointing else model_init_kwargs.get("use_cache")
            )
            config = VLLMConfigs["HumanOmni_qwen2"].from_pretrained(
                model, 
                trust_remote_code=True
            )
            config.mm_vision_tower = '/data/wuyang/PLM/siglip-base-patch16-224'
            config.mm_audio_tower = '/data/wuyang/PLM/whisper-large-v3'
            model = VLLMs["HumanOmni_qwen2"].from_pretrained(
                model,
                config=config,
                cache_dir=None,
                torch_dtype=torch.bfloat16,
                do_sample=True
            )
            vision_tower = model.get_vision_tower()
            if not vision_tower.is_loaded:
                vision_tower.load_model()

            audio_tower = model.get_audio_tower()
            if not audio_tower.is_loaded:
                audio_tower.load_model()

            audio_tower = model.get_audio_tower()
            self.audio_processor = WhisperFeatureExtractor.from_pretrained(config.mm_audio_tower)

            vision_tower = model.get_vision_tower()
            self.visual_processor = SiglipImageProcessor.from_pretrained(config.mm_vision_tower)

        else:
            model_id = model.config._name_or_path
            if args.model_init_kwargs is not None:
                raise ValueError(
                    "You passed `model_init_kwargs` to the `GRPOConfig`, but your model is already instantiated. "
                    "This argument can only be used when the `model` argument is a string."
                )

        if peft_config is not None:
            model = get_peft_model(model, peft_config)

        # Reference model

        self.ref_model = VLLMs["HumanOmni_qwen2"].from_pretrained(
            model_name_d,
            config=config,
            cache_dir=None,
            torch_dtype=torch.bfloat16,
            do_sample=True
        )
        vision_tower = self.ref_model.get_vision_tower()
        if not vision_tower.is_loaded:
            vision_tower.load_model()

        audio_tower = self.ref_model.get_audio_tower()
        if not audio_tower.is_loaded:
            audio_tower.load_model()

        bert_model = "/data/wuyang/PLM/bert-base-uncased"
        self.bert_tokenizer = BertTokenizer.from_pretrained(bert_model)


        # Processing class
        if processing_class is None:
            processing_class = processing_class = AutoProcessor.from_pretrained("/data/wuyang/PLM/Qwen2-VL-2B-Instruct")
            pad_token_id = processing_class.tokenizer.pad_token_id
            processing_class.pad_token_id = pad_token_id
            processing_class.eos_token_id = processing_class.tokenizer.eos_token_id

        # Reward functions
        if not isinstance(reward_funcs, list):
            reward_funcs = [reward_funcs]
        for i, reward_func in enumerate(reward_funcs):
            if isinstance(reward_func, str):
                reward_funcs[i] = AutoModelForSequenceClassification.from_pretrained(
                    reward_func, num_labels=1, **model_init_kwargs
                )
        self.reward_funcs = reward_funcs

        # Reward processing class
        if reward_processing_classes is None:
            reward_processing_classes = [None] * len(reward_funcs)
        elif not isinstance(reward_processing_classes, list):
            reward_processing_classes = [reward_processing_classes]
        else:
            if len(reward_processing_classes) != len(reward_funcs):
                raise ValueError("The number of reward processing classes must match the number of reward functions.")

        for i, (reward_processing_class, reward_func) in enumerate(zip(reward_processing_classes, reward_funcs)):
            if isinstance(reward_func, PreTrainedModel):
                if reward_processing_class is None:
                    reward_processing_class = AutoTokenizer.from_pretrained(reward_func.config._name_or_path)
                if reward_processing_class.pad_token_id is None:
                    reward_processing_class.pad_token = reward_processing_class.eos_token
                # The reward model computes the reward for the latest non-padded token in the input sequence.
                # So it's important to set the pad token ID to the padding token ID of the processing class.
                reward_func.config.pad_token_id = reward_processing_class.pad_token_id
                reward_processing_classes[i] = reward_processing_class
        self.reward_processing_classes = reward_processing_classes

        # Data collator
        def data_collator(features):  # No data collation is needed in GRPO
            return features

        # Training arguments
        self.max_prompt_length = args.max_prompt_length
        self.max_completion_length = args.max_completion_length  # = |o_i| in the GRPO paper
        self.num_generations = args.num_generations  # = G in the GRPO paper
        self.generation_config = GenerationConfig(
            max_new_tokens=self.max_completion_length,
            do_sample=True,  
            temperature=1, # HACK
            num_return_sequences=self.num_generations,
            pad_token_id=pad_token_id,
        )
        self.beta = args.beta

        # 难度度量配置
        self.grpo_generations = 4  # 用于GRPO训练的完整模态数量（从6改为4以节省显存）
        self.single_modal_generations = 2  # 单模态数量（视觉+音频）
        self.total_generations = self.grpo_generations + self.single_modal_generations  # 总生成数量

        # The trainer estimates the number of FLOPs (floating-point operations) using the number of elements in the
        # input tensor associated with the key "input_ids". However, in GRPO, the sampled data does not include the
        # "input_ids" key. Instead, the available keys is "prompt". As a result, the trainer issues the warning:
        # "Could not estimate the number of tokens of the input, floating-point operations will not be computed." To
        # suppress this warning, we set the "estimate_tokens" key in the model's "warnings_issued" dictionary to True.
        # This acts as a flag to indicate that the warning has already been issued.
        model.warnings_issued["estimate_tokens"] = True

        # Initialize the metrics
        self._metrics = defaultdict(list)

        super().__init__(
            model=model,
            args=args,
            data_collator=data_collator,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            processing_class=processing_class,
            callbacks=callbacks,
            optimizers=optimizers,
        )

        # Gradient accumulation requires scaled loss. Normally, loss scaling in the parent class depends on whether the
        # model accepts loss-related kwargs. Since we compute our own loss, this check is irrelevant. We set
        # self.model_accepts_loss_kwargs to False to enable scaling.
        self.model_accepts_loss_kwargs = False

        if self.ref_model is not None:
            if self.is_deepspeed_enabled:
                self.ref_model = prepare_deepspeed(self.ref_model, self.accelerator)
            else:
                self.ref_model = self.accelerator.prepare_model(self.ref_model, evaluation_mode=True)

        for i, reward_func in enumerate(self.reward_funcs):
            if isinstance(reward_func, PreTrainedModel):
                self.reward_funcs[i] = self.accelerator.prepare_model(reward_func, evaluation_mode=True)

    def _set_signature_columns_if_needed(self):
        # If `self.args.remove_unused_columns` is True, non-signature columns are removed.
        # By default, this method sets `self._signature_columns` to the model's expected inputs.
        # In GRPOTrainer, we preprocess data, so using the model's signature columns doesn't work.
        # Instead, we set them to the columns expected by the `training_step` method, hence the override.
        if self._signature_columns is None:
            self._signature_columns = ["prompt"]


    # Get the per-token log probabilities for the completions for the model and the reference model
    def _get_per_token_logps(self, model, input_ids, attention_mask, pixel_values, image_grid_thw):
        logits = model(input_ids, attention_mask=attention_mask, pixel_values=pixel_values, image_grid_thw=image_grid_thw).logits  # (B, L, V)
        logits = logits[:, :-1, :]  # (B, L-1, V), exclude the last logit: it corresponds to the next token pred
        input_ids = input_ids[:, 1:]  # (B, L-1), exclude the first input ID since we don't have logits for it
        # Compute the log probabilities for the input tokens. Use a loop to reduce memory peak.
        per_token_logps = []
        for logits_row, input_ids_row in zip(logits, input_ids):
            log_probs = logits_row.log_softmax(dim=-1)
            token_log_prob = torch.gather(log_probs, dim=1, index=input_ids_row.unsqueeze(1)).squeeze(1)
            per_token_logps.append(token_log_prob)
        return torch.stack(per_token_logps)
    
    def _get_per_token_logps_video(self, model, input_ids, attention_mask, images, audios,prompts, answer_length ):
        logits = model(input_ids, attention_mask=attention_mask, images=images, audios=audios, prompts=prompts).logits  # (B, L, V)
       # import ipdb;ipdb.set_trace()
        logits = logits[:, :-1, :]  # (B, L-1, V), exclude the last logit: it corresponds to the next token pred
        input_ids = input_ids[:, 1:]  # (B, L-1), exclude the first input ID since we don't have logits for it
        logits = logits[:, (-answer_length) :]
        input_ids = input_ids[:, -(answer_length) :]
        # Compute the log probabilities for the input tokens. Use a loop to reduce memory peak.
        per_token_logps = []
        for logits_row, input_ids_row in zip(logits, input_ids):
            log_probs = logits_row.log_softmax(dim=-1)
            token_log_prob = torch.gather(log_probs, dim=1, index=input_ids_row.unsqueeze(1)).squeeze(1)
            per_token_logps.append(token_log_prob)
        return torch.stack(per_token_logps)

    def _extract_last_answer(self, text):
        """提取最后一个answer标签的内容"""
        import re
        pattern = r'<answer>\s*(.*?)\s*</answer>'
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)

        if matches:
            return matches[-1].strip()  # 取最后一个
        return None

    def _locate_content_in_sequence(self, answer_content, seq):
        """定位answer内容在token序列中的位置，从后往前搜索确保找到最后的answer"""
        # 将answer内容编码为tokens
        answer_tokens = self.processing_class.tokenizer.encode(answer_content, add_special_tokens=False)
        print(answ)
        if len(answer_tokens) == 0:
            return torch.zeros_like(seq, dtype=torch.bool)

        # 在序列中从后往前查找匹配的位置（确保找到最后一个answer）
        seq_list = seq.tolist()
        mask = torch.zeros_like(seq, dtype=torch.bool, device=seq.device)

        # 从后往前搜索，找到最后一个匹配
        for i in range(len(seq_list) - len(answer_tokens), -1, -1):
            if seq_list[i:i+len(answer_tokens)] == answer_tokens:
                mask[i:i+len(answer_tokens)] = True
                break  # 找到最后一个匹配就停止

        return mask

    def _compute_answer_confidence(self, per_token_logps, completion_ids, completion_mask):
        """计算answer标签内容的置信度"""
        confidences = []
        device = per_token_logps.device  # 获取正确的设备

        for logps, seq, comp_mask in zip(per_token_logps, completion_ids, completion_mask):
            text = self.processing_class.tokenizer.decode(seq, skip_special_tokens=False)
            answer_content = self._extract_last_answer(text)
            print(text)
            print("___")
            print(answer_content)
            if answer_content is not None:
                answer_mask = self._locate_content_in_sequence(answer_content, seq)
                print(answer_mask)
                valid_mask = (answer_mask & comp_mask).bool()
                if valid_mask.sum() > 0:
                    answer_logps = logps[valid_mask]
                    confidence = torch.exp(answer_logps.mean())
                    print(answer_logps,confidence)
                else:
                    # 如果没有找到有效的answer token，给一个很低的置信度
                    # 这种情况理论上不应该发生，但作为安全检查
                    print()
                    confidence = torch.tensor(0.0, device=device)
                    print(f"Warning: No valid answer tokens found for content: '{answer_content}'")
            else:
                confidence = torch.tensor(0.0, device=device)

            confidences.append(confidence)

        return torch.stack(confidences)

    def _detect_modal_conflict(self, visual_completion_ids, audio_completion_ids):
        """检测模态冲突：比较answer文本内容"""
        conflicts = []
        device = visual_completion_ids.device  # 获取正确的设备

        for visual_seq, audio_seq in zip(visual_completion_ids, audio_completion_ids):
            visual_text = self.processing_class.tokenizer.decode(visual_seq, skip_special_tokens=False)
            audio_text = self.processing_class.tokenizer.decode(audio_seq, skip_special_tokens=False)

            # print(visual_text,audio_text)
            visual_answer = self._extract_last_answer(visual_text)
            audio_answer = self._extract_last_answer(audio_text)

            # print(visual_answer, audio_answer)
            if visual_answer is not None and audio_answer is not None:
                conflict = (visual_answer.strip().lower() != audio_answer.strip().lower())
            else:
                conflict = True  # 格式错误认为是冲突

            conflicts.append(conflict)

        return torch.tensor(conflicts, dtype=torch.bool, device=device)

    def _detect_confidence_anomaly(self, conf_full, conf_visual, conf_audio, threshold=0.1):
        """检测置信度异常提升"""
        visual_boost = conf_visual - conf_full
        audio_boost = conf_audio - conf_full

        return {
            'visual_anomaly': visual_boost > threshold,
            'audio_anomaly': audio_boost > threshold,
            'visual_boost': visual_boost,
            'audio_boost': audio_boost
        }

    def _compute_difficulty_score_binary(self, modal_conflict, visual_anomaly, audio_anomaly):
        """二值难度判断：easy(0) 或 hard(1)"""
        difficulty_scores = []
        device = modal_conflict.device  # 获取正确的设备

        for conflict, v_anomaly, a_anomaly in zip(modal_conflict, visual_anomaly, audio_anomaly):
            # 只要满足任一条件就是hard
            if conflict or v_anomaly or a_anomaly:
                score = 1  # hard
            else:
                score = 0  # easy

            difficulty_scores.append(score)

        return torch.tensor(difficulty_scores, dtype=torch.int, device=device)

    def _prepare_extended_inputs(self, prompt_inputs):
        """准备扩展输入：grpo_generations个完整模态 + 1个仅视觉 + 1个仅音频"""

        # 使用配置的完整模态数量
        grpo_gens = self.grpo_generations

        # grpo_generations个完整模态
        images_full = prompt_inputs['images'].repeat_interleave(grpo_gens, dim=0)
        audios_full = prompt_inputs['audios'].repeat_interleave(grpo_gens, dim=0)
        prompts_full = {k: v.repeat_interleave(grpo_gens, dim=0) for k, v in prompt_inputs['prompts'].items()}
        inputs_full = prompt_inputs['inputs'].repeat_interleave(grpo_gens, dim=0)
        attention_mask_full = prompt_inputs['attention_mask'].repeat_interleave(grpo_gens, dim=0)

        # 1个仅视觉模态（音频用零张量mask）
        images_visual = prompt_inputs['images']
        audios_visual = torch.zeros_like(prompt_inputs['audios'])
        prompts_visual = prompt_inputs['prompts']
        inputs_visual = prompt_inputs['inputs']
        attention_mask_visual = prompt_inputs['attention_mask']

        # 1个仅音频模态（视觉用零张量mask）
        images_audio = torch.zeros_like(prompt_inputs['images'])
        audios_audio = prompt_inputs['audios']
        prompts_audio = prompt_inputs['prompts']
        inputs_audio = prompt_inputs['inputs']
        attention_mask_audio = prompt_inputs['attention_mask']

        # 合并所有输入
        extended_inputs = {
            'inputs': torch.cat([inputs_full, inputs_visual, inputs_audio], dim=0),
            'images': torch.cat([images_full, images_visual, images_audio], dim=0),
            'audios': torch.cat([audios_full, audios_visual, audios_audio], dim=0),
            'attention_mask': torch.cat([attention_mask_full, attention_mask_visual, attention_mask_audio], dim=0),
            'prompts': {
                k: torch.cat([prompts_full[k], prompts_visual[k], prompts_audio[k]], dim=0)
                for k in prompt_inputs['prompts'].keys()
            }
        }

        return extended_inputs

    def _compute_difficulty_metrics(self, full_modal_ids, visual_only_ids, audio_only_ids,
                                  full_logps, visual_logps, audio_logps, completion_mask):
        """计算完整的难度度量"""

        # 计算置信度
        grpo_gens = self.grpo_generations
        batch_size = len(full_modal_ids) // grpo_gens  # 原始batch大小

        # 对于置信度计算，我们需要对grpo_gens个完整模态的结果取平均
        conf_full_all = self._compute_answer_confidence(full_logps, full_modal_ids, completion_mask[:len(full_modal_ids)])
        conf_full = conf_full_all.view(batch_size, grpo_gens).mean(dim=1)  # 对grpo_gens个生成取平均

        conf_visual = self._compute_answer_confidence(visual_logps, visual_only_ids, completion_mask[len(full_modal_ids):len(full_modal_ids)+batch_size])
        conf_audio = self._compute_answer_confidence(audio_logps, audio_only_ids, completion_mask[len(full_modal_ids)+batch_size:])

        # 检测模态冲突
        modal_conflict = self._detect_modal_conflict(visual_only_ids, audio_only_ids)

        # 检测置信度异常
        anomaly_results = self._detect_confidence_anomaly(conf_full, conf_visual, conf_audio, threshold=0.1)

        # 二值难度判断
        difficulty_score = self._compute_difficulty_score_binary(
            modal_conflict,
            anomaly_results['visual_anomaly'],
            anomaly_results['audio_anomaly']
        )

        return {
            'modal_conflict': modal_conflict,                    # [batch_size] bool
            'visual_anomaly': anomaly_results['visual_anomaly'], # [batch_size] bool
            'audio_anomaly': anomaly_results['audio_anomaly'],   # [batch_size] bool
            'visual_boost': anomaly_results['visual_boost'],     # [batch_size] float
            'audio_boost': anomaly_results['audio_boost'],       # [batch_size] float
            'difficulty_score': difficulty_score,                # [batch_size] int (0=easy, 1=hard)
            'confidences': {
                'full': conf_full,     # [batch_size] float
                'visual': conf_visual, # [batch_size] float
                'audio': conf_audio    # [batch_size] float
            }
        }

    # Trainer "prepares" the inputs before calling `compute_loss`. It converts to tensor and move to device.
    # Since we preprocess the data in `compute_loss`, we need to override this method to skip this step.
    def _prepare_inputs(self, inputs: dict[str, Union[torch.Tensor, Any]]) -> dict[str, Union[torch.Tensor, Any]]:
        return inputs
    
    def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
        if return_outputs:
            raise ValueError("The GRPOTrainer does not support returning outputs")

        prompts = []
        bert_prompts = []
        for x in inputs:
            prompt = x["prompt"]
            bert_prompts.append(prompt[0]['content'][1]['text'])
            text = prompt[0]["content"][0].pop('text')
            video_path = x["video"]
            prompt[0]["content"][0]["video"] = x["video"]
            prompt[0]['content'][1]['text'] = '<vi_start><video><vi_end>\n<au_start><audio><au_end>\n' + prompt[0]['content'][1]['text']
            prompts.append(prompt)

        bert_prompts = self.bert_tokenizer(bert_prompts, return_tensors='pt', padding=True, truncation=True,add_special_tokens=True)

        prompts_text = []
        for example in inputs:
            prompt_text = maybe_apply_chat_template(example, self.processing_class)["prompt"]
            prompts_text.append(prompt_text)

        input_ids = [tokenizer_multimodal_token(prompts_text_, self.processing_class.tokenizer, '<video>', return_tensors='pt') for prompts_text_ in prompts_text]
        input_ids = torch.cat(input_ids, dim=0).unsqueeze(0)
        video = []
        audios = []
        for prompt in prompts:
            video_file = prompt[0]["content"][0]["video"]
            video_ids = process_video(video_file, self.visual_processor, aspect_ratio="pad", num_frames=8)
            video.append(video_ids)

            audio, audio_sample_rate = process_audio(video_file)
            audio = self.audio_processor(audio, sampling_rate=audio_sample_rate, return_tensors='pt')['input_features']
            audios.append(audio)
        video = torch.cat(video, dim=0).unsqueeze(0)
        audios = torch.cat(audios, dim=0).unsqueeze(0)

        attention_masks = input_ids.ne(self.processing_class.pad_token_id)
        prompt_inputs = {}
        prompt_inputs['inputs'] = input_ids
        prompt_inputs['images'] = video 
        prompt_inputs['attention_mask'] = attention_masks
        prompt_inputs['prompts'] = bert_prompts
        prompt_inputs['audios'] = audios

        prompt_inputs = super()._prepare_inputs(prompt_inputs)

        prompt_ids, prompt_mask = prompt_inputs["inputs"], prompt_inputs["attention_mask"]


        if self.max_prompt_length is not None:
            prompt_ids = prompt_ids[:, -self.max_prompt_length :]
            prompt_mask = prompt_mask[:, -self.max_prompt_length :]

        # 准备扩展输入：grpo_generations个完整模态 + 1个仅视觉 + 1个仅音频
        prompt_inputs_extended = self._prepare_extended_inputs(prompt_inputs)

        # 修改generation_config为total_generations个生成
        self.generation_config.num_return_sequences = self.total_generations

        # Generate completions
        with unwrap_model_for_generation(model, self.accelerator) as unwrapped_model:
            prompt_completion_ids = unwrapped_model.generate(**prompt_inputs_extended, generation_config=self.generation_config)
            answer_length = prompt_completion_ids.size(1)

        # 分离结果
        batch_size = len(inputs)
        grpo_gens = self.grpo_generations
        full_modal_ids = prompt_completion_ids[:batch_size*grpo_gens]      # 前grpo_gens个：完整模态
        visual_only_ids = prompt_completion_ids[batch_size*grpo_gens:batch_size*(grpo_gens+1)]  # 仅视觉
        audio_only_ids = prompt_completion_ids[batch_size*(grpo_gens+1):batch_size*(grpo_gens+2)]   # 仅音频

        # 使用完整模态的结果进行后续处理
        completion_ids = full_modal_ids
        prompt_mask = prompt_mask.repeat_interleave(grpo_gens, dim=0)  # 使用grpo_gens
        prompt_ids_repeat = prompt_ids.repeat_interleave(grpo_gens, dim=0)  # 使用grpo_gens

        # 为所有total_generations个生成计算completion_mask
        is_eos_all = prompt_completion_ids == self.processing_class.eos_token_id
        device = self.accelerator.device
        eos_idx_all = torch.full((is_eos_all.size(0),), is_eos_all.size(1), dtype=torch.long, device=device)
        eos_idx_all[is_eos_all.any(dim=1)] = is_eos_all.int().argmax(dim=1)[is_eos_all.any(dim=1)]
        sequence_indices_all = torch.arange(is_eos_all.size(1), device=device).expand(is_eos_all.size(0), -1)
        completion_mask_all = (sequence_indices_all <= eos_idx_all.unsqueeze(1)).int()

        # 分离不同模态的completion_mask
        grpo_gens = self.grpo_generations
        completion_mask = completion_mask_all[:batch_size*grpo_gens]  # 前grpo_gens个：完整模态
        completion_mask_visual = completion_mask_all[batch_size*grpo_gens:batch_size*(grpo_gens+1)]  # 仅视觉
        completion_mask_audio = completion_mask_all[batch_size*(grpo_gens+1):batch_size*(grpo_gens+2)]   # 仅音频

        # 为完整模态计算attention_mask
        attention_mask = torch.cat([prompt_mask, completion_mask], dim=1)  # (B*grpo_gens, P+C)
        
        prompt_completion_ids_repeat = torch.cat([prompt_ids_repeat, completion_ids], dim=1)

        grpo_gens = self.grpo_generations
        images_repeat = prompt_inputs['images'].repeat_interleave(grpo_gens, dim=0)
        audios_repeat = prompt_inputs['audios'].repeat_interleave(grpo_gens, dim=0)
        prompts_repeat = {}
        prompts_repeat['input_ids'] =  prompt_inputs['prompts']['input_ids'].repeat_interleave(grpo_gens, dim=0)
        prompts_repeat['token_type_ids'] =  prompt_inputs['prompts']['token_type_ids'].repeat_interleave(grpo_gens, dim=0)
        prompts_repeat['attention_mask'] =  prompt_inputs['prompts']['attention_mask'].repeat_interleave(grpo_gens, dim=0)


        per_token_logps = self._get_per_token_logps_video(model, prompt_completion_ids_repeat, attention_mask, images_repeat, audios_repeat, prompts_repeat, answer_length)

        # 计算单模态的logps（用于难度度量）
        # 为单模态准备正确的attention_mask
        prompt_mask_visual = prompt_inputs['attention_mask']  # 原始prompt mask
        prompt_mask_audio = prompt_inputs['attention_mask']   # 原始prompt mask

        attention_mask_visual = torch.cat([prompt_mask_visual, completion_mask_visual], dim=1)
        attention_mask_audio = torch.cat([prompt_mask_audio, completion_mask_audio], dim=1)

        visual_per_token_logps = self._get_per_token_logps_video(
            model,
            torch.cat([prompt_ids, visual_only_ids], dim=1),  # 完整的input_ids
            attention_mask_visual,
            prompt_inputs_extended['images'][batch_size*grpo_gens:batch_size*(grpo_gens+1)],
            prompt_inputs_extended['audios'][batch_size*grpo_gens:batch_size*(grpo_gens+1)],
            {k: v[batch_size*grpo_gens:batch_size*(grpo_gens+1)] for k, v in prompt_inputs_extended['prompts'].items()},
            answer_length
        )

        audio_per_token_logps = self._get_per_token_logps_video(
            model,
            torch.cat([prompt_ids, audio_only_ids], dim=1),  # 完整的input_ids
            attention_mask_audio,
            prompt_inputs_extended['images'][batch_size*(grpo_gens+1):batch_size*(grpo_gens+2)],
            prompt_inputs_extended['audios'][batch_size*(grpo_gens+1):batch_size*(grpo_gens+2)],
            {k: v[batch_size*(grpo_gens+1):batch_size*(grpo_gens+2)] for k, v in prompt_inputs_extended['prompts'].items()},
            answer_length
        )

        with torch.inference_mode():
            if self.ref_model is not None:
                ref_per_token_logps = self._get_per_token_logps_video(self.ref_model, prompt_completion_ids_repeat, attention_mask, images_repeat, audios_repeat, prompts_repeat, answer_length )
            else:
                with self.accelerator.unwrap_model(model).disable_adapter():
                    ref_per_token_logps = self._get_per_token_logps_video(model, prompt_completion_ids_repeat, attention_mask, images_repeat, audios_repeat, prompts_repeat, answer_length)
        ref_per_token_logps = ref_per_token_logps

        per_token_kl = torch.exp(ref_per_token_logps - per_token_logps) - (ref_per_token_logps - per_token_logps) - 1

        # 计算难度度量
        difficulty_metrics = self._compute_difficulty_metrics(
            completion_ids, visual_only_ids, audio_only_ids,
            per_token_logps, visual_per_token_logps, audio_per_token_logps,
            torch.cat([completion_mask, completion_mask_visual, completion_mask_audio], dim=0)
        )

        # Decode the generated completions
        completions = self.processing_class.batch_decode(completion_ids, skip_special_tokens=True)
        if is_conversational(inputs[0]):
            completions = [[{"role": "assistant", "content": completion}] for completion in completions]

        # Compute the rewards
        grpo_gens = self.grpo_generations
        prompts = [prompt for prompt in prompts for _ in range(grpo_gens)]  # 使用grpo_gens

        rewards_per_func = torch.zeros(len(prompts), len(self.reward_funcs), device=device)
        for i, (reward_func, reward_processing_class) in enumerate(
            zip(self.reward_funcs, self.reward_processing_classes)
        ):
            if isinstance(reward_func, PreTrainedModel):
                if is_conversational(inputs[0]):
                    messages = [{"messages": p + c} for p, c in zip(prompts, completions)]
                    texts = [apply_chat_template(x, reward_processing_class)["text"] for x in messages]
                else:
                    texts = [p + c for p, c in zip(prompts, completions)]
                reward_inputs = reward_processing_class(
                    texts, return_tensors="pt", padding=True, padding_side="right", add_special_tokens=False
                )
                reward_inputs = super()._prepare_inputs(reward_inputs)
                with torch.inference_mode():
                    rewards_per_func[:, i] = reward_func(**reward_inputs).logits[:, 0]  # Shape (B*G,)
            else:
                # Repeat all input columns (but "prompt" and "completion") to match the number of generations
                reward_kwargs = {key: [] for key in inputs[0].keys() if key not in ["prompt", "completion"]}
                grpo_gens = self.grpo_generations
                for key in reward_kwargs:
                    for example in inputs:
                        # Repeat each value in the column for grpo_gens times
                        reward_kwargs[key].extend([example[key]] * grpo_gens)

                # 添加difficulty_metrics到reward_kwargs
                reward_kwargs['difficulty_metrics'] = difficulty_metrics

                output_reward_func = reward_func(prompts=prompts, completions=completions, **reward_kwargs)
                rewards_per_func[:, i] = torch.tensor(output_reward_func, dtype=torch.float32, device=device)

        # print(rewards_per_func)
        # Apply weights to rewards_per_func first
        TMPW1 = 0.5
        TMPW2 = 0.5
        weights = torch.tensor([1.0, TMPW1, TMPW2], device=rewards_per_func.device)
        rewards_per_func = rewards_per_func * weights
        # Sum the weighted rewards
        rewards = rewards_per_func.sum(dim=1)
        # print(rewards)

        # Compute grouped-wise rewards (using grpo_gens instead of num_generations)
        grpo_gens = self.grpo_generations
        mean_grouped_rewards = rewards.view(-1, grpo_gens).mean(dim=1)
        std_grouped_rewards = rewards.view(-1, grpo_gens).std(dim=1)

        # Normalize the rewards to compute the advantages
        mean_grouped_rewards = mean_grouped_rewards.repeat_interleave(grpo_gens, dim=0)
        std_grouped_rewards = std_grouped_rewards.repeat_interleave(grpo_gens, dim=0)
        advantages = (rewards - mean_grouped_rewards) / (std_grouped_rewards + 1e-4)

        # x - x.detach() allows for preserving gradients from x
        per_token_loss = torch.exp(per_token_logps - per_token_logps.detach()) * advantages.unsqueeze(1)
        per_token_loss = -(per_token_loss - self.beta * per_token_kl)
        loss = ((per_token_loss * completion_mask).sum(dim=1) / completion_mask.sum(dim=1)).mean()

        # Log the metrics
        completion_length = self.accelerator.gather_for_metrics(completion_mask.sum(1)).float().mean().item()
        self._metrics["completion_length"].append(completion_length)

        reward_per_func = self.accelerator.gather_for_metrics(rewards_per_func).mean(0)
        for i, reward_func in enumerate(self.reward_funcs):
            if isinstance(reward_func, PreTrainedModel):
                reward_func_name = reward_func.config._name_or_path.split("/")[-1]
            else:
                reward_func_name = reward_func.__name__
            self._metrics[f"rewards/{reward_func_name}"].append(reward_per_func[i].item())

        self._metrics["reward"].append(self.accelerator.gather_for_metrics(rewards).mean().item())

        self._metrics["reward_std"].append(self.accelerator.gather_for_metrics(std_grouped_rewards).mean().item())

        mean_kl = ((per_token_kl * completion_mask).sum(dim=1) / completion_mask.sum(dim=1)).mean()
        self._metrics["kl"].append(self.accelerator.gather_for_metrics(mean_kl).mean().item())

        # print(difficulty_metrics)
        # print(difficulty_metrics['modal_conflict'].float())
        # print(difficulty_metrics['modal_conflict'].float().mean())
        # print(difficulty_metrics['modal_conflict'].float().mean().item())
        # 添加难度度量的日志
        if difficulty_metrics is not None:
            self._metrics["difficulty/modal_conflict_rate"].append(difficulty_metrics['modal_conflict'].float().mean().item())
            self._metrics["difficulty/visual_anomaly_rate"].append(difficulty_metrics['visual_anomaly'].float().mean().item())
            self._metrics["difficulty/audio_anomaly_rate"].append(difficulty_metrics['audio_anomaly'].float().mean().item())
            self._metrics["difficulty/hard_sample_rate"].append(difficulty_metrics['difficulty_score'].float().mean().item())
            self._metrics["difficulty/avg_visual_boost"].append(difficulty_metrics['visual_boost'].mean().item())
            self._metrics["difficulty/avg_audio_boost"].append(difficulty_metrics['audio_boost'].mean().item())

        # 恢复原始设置
        self.generation_config.num_return_sequences = self.num_generations

        return loss

    def log(self, logs: dict[str, float], start_time: Optional[float] = None) -> None:
        metrics = {key: sum(val) / len(val) for key, val in self._metrics.items()}  # average the metrics
        logs = {**logs, **metrics}
        if version.parse(transformers.__version__) >= version.parse("4.47.0.dev0"):
            super().log(logs, start_time)
        else:  # transformers<=4.46
            super().log(logs)
        self._metrics.clear()

    def create_model_card(
        self,
        model_name: Optional[str] = None,
        dataset_name: Optional[str] = None,
        tags: Union[str, list[str], None] = None,
    ):
        """
        Creates a draft of a model card using the information available to the `Trainer`.

        Args:
            model_name (`str` or `None`, *optional*, defaults to `None`):
                Name of the model.
            dataset_name (`str` or `None`, *optional*, defaults to `None`):
                Name of the dataset used for training.
            tags (`str`, `list[str]` or `None`, *optional*, defaults to `None`):
                Tags to be associated with the model card.
        """
        if not self.is_world_process_zero():
            return

        if hasattr(self.model.config, "_name_or_path") and not os.path.isdir(self.model.config._name_or_path):
            base_model = self.model.config._name_or_path
        else:
            base_model = None

        tags = tags or []
        if isinstance(tags, str):
            tags = [tags]

        if hasattr(self.model.config, "unsloth_version"):
            tags.append("unsloth")

        citation = textwrap.dedent(
            """\
            @article{zhihong2024deepseekmath,
                title        = {{DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models}},
                author       = {Zhihong Shao and Peiyi Wang and Qihao Zhu and Runxin Xu and Junxiao Song and Mingchuan Zhang and Y. K. Li and Y. Wu and Daya Guo},
                year         = 2024,
                eprint       = {arXiv:2402.03300},
            """
        )

        model_card = generate_model_card(
            base_model=base_model,
            model_name=model_name,
            hub_model_id=self.hub_model_id,
            dataset_name=dataset_name,
            tags=tags,
            wandb_url=wandb.run.get_url() if is_wandb_available() and wandb.run is not None else None,
            comet_url=get_comet_experiment_url(),
            trainer_name="GRPO",
            trainer_citation=citation,
            paper_title="DeepSeekMath: Pushing the Limits of Mathematical Reasoning in Open Language Models",
            paper_id="2402.03300",
        )

        model_card.save(os.path.join(self.args.output_dir, "README.md"))
