------------- 15-14-11-15-782676 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=0.000
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.000
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=0.000
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=0.000
------------- 15-14-11-15-782877 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 15-14-11-15-784294 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 15-14-11-15-785245 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 15-14-11-42-020056 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 932.5
Completion 0: length=913, L/Lavg=0.979, ra=0.0, reward=0.000
Completion 1: length=1030, L/Lavg=1.105, ra=0.0, reward=0.000
Completion 2: length=811, L/Lavg=0.870, ra=0.0, reward=0.000
Completion 3: length=976, L/Lavg=1.047, ra=0.0, reward=0.000
------------- 15-14-11-42-023734 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 624.75
Completion 0: length=662, L/Lavg=1.060, ra=0.0, reward=0.000
Completion 1: length=671, L/Lavg=1.074, ra=0.0, reward=0.000
Completion 2: length=442, L/Lavg=0.707, ra=0.0, reward=0.000
Completion 3: length=724, L/Lavg=1.159, ra=0.0, reward=0.000
------------- 15-14-11-42-150750 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 686.0
Completion 0: length=763, L/Lavg=1.112, ra=0.0, reward=0.000
Completion 1: length=729, L/Lavg=1.063, ra=0.0, reward=0.000
Completion 2: length=776, L/Lavg=1.131, ra=1.0, reward=-0.131
Completion 3: length=476, L/Lavg=0.694, ra=0.0, reward=0.000
------------- 15-14-11-42-445315 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 888.75
Completion 0: length=684, L/Lavg=0.770, ra=0.0, reward=0.000
Completion 1: length=828, L/Lavg=0.932, ra=1.0, reward=0.068
Completion 2: length=872, L/Lavg=0.981, ra=1.0, reward=0.019
Completion 3: length=1171, L/Lavg=1.318, ra=1.0, reward=-0.318
------------- 15-14-21-06-384648 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 15-14-21-06-385894 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.25
Completion 0: length=582, L/Lavg=0.774, ra=0.0, reward=0.000
Completion 1: length=1264, L/Lavg=1.680, ra=0.0, reward=0.000
Completion 2: length=743, L/Lavg=0.988, ra=0.0, reward=0.000
Completion 3: length=420, L/Lavg=0.558, ra=0.0, reward=0.000
------------- 15-14-21-06-391042 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 15-14-21-06-405887 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 811.25
Completion 0: length=1146, L/Lavg=1.413, ra=1.0, reward=0.000
Completion 1: length=796, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 2: length=574, L/Lavg=0.708, ra=0.0, reward=-0.292
Completion 3: length=729, L/Lavg=0.899, ra=1.0, reward=0.000
------------- 16-14-42-22-093110 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-14-42-22-100223 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-14-47-44-082617 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-14-47-44-084564 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-14-59-32-929374 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-14-59-32-936931 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-01-50-083578 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-01-50-091403 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 846.75
Completion 0: length=748, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 1: length=924, L/Lavg=1.091, ra=0.0, reward=0.000
Completion 2: length=894, L/Lavg=1.056, ra=0.0, reward=0.000
Completion 3: length=821, L/Lavg=0.970, ra=0.0, reward=0.000
------------- 16-15-02-11-583141 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 960.75
Completion 0: length=1054, L/Lavg=1.097, ra=0.0, reward=0.000
Completion 1: length=595, L/Lavg=0.619, ra=0.0, reward=0.000
Completion 2: length=1085, L/Lavg=1.129, ra=0.0, reward=0.000
Completion 3: length=1109, L/Lavg=1.154, ra=0.0, reward=0.000
------------- 16-15-02-11-637490 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 991.25
Completion 0: length=960, L/Lavg=0.968, ra=1.0, reward=0.032
Completion 1: length=1433, L/Lavg=1.446, ra=1.0, reward=-0.446
Completion 2: length=766, L/Lavg=0.773, ra=0.0, reward=0.000
Completion 3: length=806, L/Lavg=0.813, ra=0.0, reward=0.000
------------- 16-15-05-09-285223 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-05-26-334630 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-15-05-39-765724 New Length Reward Debug -------------
Group Accuracy: 0.750 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 752.5
Completion 0: length=826, L/Lavg=1.098, ra=1.0, reward=-0.098
Completion 1: length=643, L/Lavg=0.854, ra=1.0, reward=0.146
Completion 2: length=723, L/Lavg=0.961, ra=1.0, reward=0.039
Completion 3: length=818, L/Lavg=1.087, ra=0.0, reward=0.000
------------- 16-15-05-54-369867 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 653.5
Completion 0: length=786, L/Lavg=1.203, ra=0.0, reward=0.000
Completion 1: length=599, L/Lavg=0.917, ra=0.0, reward=0.000
Completion 2: length=501, L/Lavg=0.767, ra=0.0, reward=0.000
Completion 3: length=728, L/Lavg=1.114, ra=0.0, reward=0.000
------------- 16-15-06-07-612575 New Length Reward Debug -------------
Group Accuracy: 1.000 (threshold: 0.6)
Problem Type: Hard (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 927.25
Completion 0: length=676, L/Lavg=0.729, ra=1.0, reward=0.000
Completion 1: length=980, L/Lavg=1.057, ra=1.0, reward=0.000
Completion 2: length=746, L/Lavg=0.805, ra=1.0, reward=0.000
Completion 3: length=1307, L/Lavg=1.410, ra=1.0, reward=0.000
------------- 16-15-06-19-013214 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 764.25
Completion 0: length=804, L/Lavg=1.052, ra=1.0, reward=-0.052
Completion 1: length=901, L/Lavg=1.179, ra=0.0, reward=0.000
Completion 2: length=803, L/Lavg=1.051, ra=0.0, reward=0.000
Completion 3: length=549, L/Lavg=0.718, ra=0.0, reward=0.000
------------- 16-15-06-30-389291 New Length Reward Debug -------------
Group Accuracy: 0.000 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 860.0
Completion 0: length=713, L/Lavg=0.829, ra=0.0, reward=0.000
Completion 1: length=1217, L/Lavg=1.415, ra=0.0, reward=0.000
Completion 2: length=799, L/Lavg=0.929, ra=0.0, reward=0.000
Completion 3: length=711, L/Lavg=0.827, ra=0.0, reward=0.000
------------- 16-15-11-26-553319 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-14-23-282625 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-14-40-796000 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
------------- 16-15-14-55-145610 New Length Reward Debug -------------
Group Accuracy: 0.500 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 852.75
Completion 0: length=933, L/Lavg=1.094, ra=0.0, reward=0.000
Completion 1: length=1058, L/Lavg=1.241, ra=0.0, reward=0.000
Completion 2: length=763, L/Lavg=0.895, ra=1.0, reward=0.105
Completion 3: length=657, L/Lavg=0.770, ra=1.0, reward=0.230
------------- 16-15-15-09-515284 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 858.25
Completion 0: length=1126, L/Lavg=1.312, ra=0.0, reward=0.000
Completion 1: length=1101, L/Lavg=1.283, ra=0.0, reward=0.000
Completion 2: length=612, L/Lavg=0.713, ra=1.0, reward=0.287
Completion 3: length=594, L/Lavg=0.692, ra=0.0, reward=0.000
------------- 16-15-16-18-832229 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-33-13-933327 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-36-22-856687 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-39-46-199269 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-41-54-327938 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-51-30-470287 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-54-41-836377 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['easy', 'easy', 'easy', 'easy']
Average Length (Lavg): 726.75
Completion 0: length=442, L/Lavg=0.608, ra=0.0, reward=0.000
Completion 1: length=953, L/Lavg=1.311, ra=0.0, reward=0.000
Completion 2: length=812, L/Lavg=1.117, ra=0.0, reward=0.000
Completion 3: length=700, L/Lavg=0.963, ra=1.0, reward=0.037
------------- 16-15-54-58-939433 New Length Reward Debug -------------
Group Accuracy: 0.250 (threshold: 0.6)
Problem Type: Easy (determined by accuracy threshold)
Original Difficulty: ['hard', 'hard', 'hard', 'hard']
Average Length (Lavg): 908.25
Completion 0: length=902, L/Lavg=0.993, ra=0.0, reward=0.000
Completion 1: length=903, L/Lavg=0.994, ra=0.0, reward=0.000
Completion 2: length=734, L/Lavg=0.808, ra=0.0, reward=0.000
Completion 3: length=1094, L/Lavg=1.205, ra=1.0, reward=-0.205
